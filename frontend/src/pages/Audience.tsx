import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { ArrowLeft, Volume2, Users } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useWebSocket } from "@/hooks/use-websocket";
import { ConfirmationModal } from "@/components/ConfirmationModal";


const Audience = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [sessionCode, setSessionCode] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [liveTextToSpeech, setLiveTextToSpeech] = useState(false);
  const [volume, setVolume] = useState([70]);
  const [originalText, setOriginalText] = useState("");
  const [translatedText, setTranslatedText] = useState("");
  const [audienceCount, setAudienceCount] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<"disconnected" | "connecting" | "connected">("disconnected");
  const [transcriptSegments, setTranscriptSegments] = useState<any[]>([]);
  const [translatedSegments, setTranslatedSegments] = useState<any[]>([]);
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [showSessionEndedModal, setShowSessionEndedModal] = useState(false);
  const [audioQueue, setAudioQueue] = useState<string[]>([]);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);

  const updateTranslationMessages = (currentMessages, newMessageData) => {
    // The data for the new message is already in the flat format
    const newPartialMessage = {
        content: newMessageData.content,
        start_time: newMessageData.start_time,
        end_time: newMessageData.end_time,
    };
    const newStartTime = newPartialMessage.start_time;

    // 1. Find the index of an existing message with the same start_time
    const existingIndex = currentMessages.findIndex(
        (msg) => msg.start_time === newStartTime
    );

    // 2. Create the new array based on the comparison
    let updatedMessages;

    if (existingIndex !== -1) {
        // CASE 1: Replacement
        // Found an existing message with the same start_time, so we replace it.
        // We create a new array copy first for immutability.
        updatedMessages = [...currentMessages];
        updatedMessages[existingIndex] = newPartialMessage;
    } else {
        // CASE 2: Append
        // No existing message found, so we append the new one.
        updatedMessages = [...currentMessages, newPartialMessage];
    }

    const combinedContent = updatedMessages
    .map(msg => msg.content)
    .join(' ');
    setTranslatedText(combinedContent);
    return updatedMessages;
  }

  // Enhanced audio playback with queue management
  const playAudio = (audioBase64: string) => {
    if (!audioBase64) return;

    try {
      const audioData = `data:audio/mpeg;base64,${audioBase64}`;

      // Stop current audio if playing
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }

      const audio = new Audio(audioData);
      setCurrentAudio(audio);
      setIsPlayingAudio(true);

      // Set audio properties for consistent playback
      audio.volume = volume[0] / 100; // Use the volume slider value
      audio.preload = 'auto';

      // Handle audio events
      audio.onended = () => {
        setIsPlayingAudio(false);
        setCurrentAudio(null);
        // Process next audio in queue if any
        processAudioQueue();
      };

      audio.onerror = (error) => {
        console.error("Error playing audio:", error);
        setIsPlayingAudio(false);
        setCurrentAudio(null);
        processAudioQueue();
      };

      // Play the audio
      audio.play().catch(error => {
        console.error("Error starting audio playback:", error);
        setIsPlayingAudio(false);
        setCurrentAudio(null);
      });

    } catch (error) {
      console.error("Error creating audio:", error);
    }
  };

  // Process audio queue
  const processAudioQueue = () => {
    if (audioQueue.length > 0 && !isPlayingAudio) {
      const nextAudio = audioQueue[0];
      setAudioQueue(prev => prev.slice(1));
      playAudio(nextAudio);
    }
  };

  // Add audio to queue
  const queueAudio = (audioBase64: string) => {
    if (isPlayingAudio) {
      setAudioQueue(prev => [...prev, audioBase64]);
    } else {
      playAudio(audioBase64);
    }
  };

  // WebSocket connection for audience
  const wsUrl = isConnected
    ? `ws://localhost:8081/ws/audience/${sessionCode}`
    : null;

  const { isConnected: wsConnected, sendMessage } = useWebSocket(wsUrl, {
    onOpen: () => {
      setConnectionStatus("connected");
      toast({
        title: "Connected to session",
        description: `Successfully joined session ${sessionCode.toUpperCase()}`,
      });
    },
    onMessage: (message) => {
      console.log("[AUDIENCE] Received WebSocket message:", message);

      // Handle both old and new message formats
      if (message.message === "PartialTranscript" && message.segments) {
        const segments = message.segments;
        setTranscriptSegments((prevSegments) => [...prevSegments, ...segments]);

        // Update display text with latest segment
        const latestSegment = segments[segments.length - 1];
        if (latestSegment?.text) {
          setOriginalText((prevOriginalText) => prevOriginalText + " " + latestSegment.text);

          if (liveTextToSpeech) {
            speakText(translatedText);
          }
        }
      } else if (message.message === "AddPartialTranslation") {
        console.log("[AUDIENCE] Received partial translation:", message.content);
        setTranslatedSegments((oldSegments) => updateTranslationMessages(oldSegments, message));
      } else if (message.message === "AudienceCount") {
        console.log("[AUDIENCE] Received audience count:", message.data);
        setAudienceCount(message.data || 0);
      } else {
        // Handle new backend message format (cast to any for flexibility)
        const backendMessage = message as any;

        // Try to parse string data as JSON
        let parsedMessage = backendMessage;
        if (typeof backendMessage.data === 'string') {
          try {
            parsedMessage = JSON.parse(backendMessage.data);
          } catch (e) {
            parsedMessage = backendMessage;
          }
        }

        if (parsedMessage && typeof parsedMessage === 'object' && 'audience_count' in parsedMessage) {
          console.log("[AUDIENCE] Received audience count (parsed JSON):", parsedMessage.audience_count);
          setAudienceCount(parsedMessage.audience_count || 0);
        } else if (parsedMessage.message === "PartialTranscript" && parsedMessage.segments) {
          // Handle backend transcript format
          const segments = parsedMessage.segments;
          setTranscriptSegments((prevSegments) => [...prevSegments, ...segments]);

          const latestSegment = segments[segments.length - 1];
          if (latestSegment?.text) {
            setOriginalText((prevOriginalText) => prevOriginalText + " " + latestSegment.text);
            if (liveTextToSpeech) {
              speakText(translatedText);
            }
          }
        } else if (parsedMessage.message === "AddTranscript") {
          setOriginalText(parsedMessage.results?.original || "");
          setTranslatedText(parsedMessage.results?.translated || "");

          // Queue TTS audio if available (ensures continuous playback)
          if (parsedMessage.results?.audio) {
            queueAudio(parsedMessage.results.audio);
          }
        } else if (parsedMessage.message === "AudienceCount") {
          setAudienceCount(parsedMessage.count || 0);
        } else if (parsedMessage.message === "SessionEnded") {
          // Show session ended modal
          setShowSessionEndedModal(true);
        } else if (parsedMessage.message === "SpeakerDisconnected") {
          toast({
            title: "Speaker disconnected",
            description: "The speaker has disconnected from the session.",
            variant: "destructive",
          });
        }
      }
    },
    onError: () => {
      setConnectionStatus("disconnected");
      toast({
        title: "Connection error",
        description: "Failed to connect to the session. Please check the session code.",
        variant: "destructive",
      });
    },
    onClose: () => {
      setConnectionStatus("disconnected");
    },
  });

  const joinSession = async () => {
    if (!sessionCode.trim()) {
      toast({
        title: "Session code required",
        description: "Please enter a session code to join.",
        variant: "destructive",
      });
      return;
    }

    try {
      setConnectionStatus("connecting");

      // First validate the session
      const validateResponse = await fetch(`http://localhost:8081/session/validate/${sessionCode}`);
      const validateData = await validateResponse.json();

      if (!validateData.valid) {
        setConnectionStatus("disconnected");
        toast({
          title: "Invalid session",
          description: validateData.error || "Session not found or inactive.",
          variant: "destructive",
        });
        return;
      }

      // Join session on backend
      const response = await fetch(`http://localhost:8081/session/join/${sessionCode}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ audience_id: `audience_${Date.now()}` }),
      });

      if (response.ok) {
        const data = await response.json();
        setSessionInfo(data.session);
        setIsConnected(true);

        toast({
          title: "Joined session",
          description: `Connected to session ${sessionCode}`,
        });
      } else {
        const errorData = await response.json();
        setConnectionStatus("disconnected");
        toast({
          title: "Failed to join session",
          description: errorData.error || "Could not join session.",
          variant: "destructive",
        });
      }
    } catch (error) {
      setConnectionStatus("disconnected");
      toast({
        title: "Connection error",
        description: "Could not connect to the server. Please check if the backend is running.",
        variant: "destructive",
      });
    }
  };

  const speakText = (text: string) => {
    if ("speechSynthesis" in window) {
      window.speechSynthesis.cancel();
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.volume = volume[0] / 100;
      utterance.lang = "zh-CN";
      window.speechSynthesis.speak(utterance);
    }
  };

  const disconnectSession = () => {
    setIsConnected(false);
    setConnectionStatus("disconnected");
    setOriginalText("");
    setTranslatedText("");
    setTranscriptSegments([]);
    setAudienceCount(0);
    setSessionCode("");
    setSessionInfo(null);
    toast({
      title: "Disconnected",
      description: "You have left the session.",
    });
  };

  const handleSessionEnded = () => {
    // Reset all state and navigate to homepage
    setIsConnected(false);
    setConnectionStatus("disconnected");
    setOriginalText("");
    setTranslatedText("");
    setTranscriptSegments([]);
    setAudienceCount(0);
    setSessionCode("");
    setSessionInfo(null);
    setShowSessionEndedModal(false);
    navigate("/");
  };

  if (!isConnected) {
    return (
      <div className="min-h-screen bg-gradient-surface p-4">
        <div className="container mx-auto max-w-2xl pt-8">
          <Button
            variant="ghost"
            onClick={() => navigate("/")}
            className="mb-6 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>

          <Card className="shadow-medium border-border/50">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl text-foreground">Join Translation Session</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Session Code</label>
                <Input
                  placeholder="Enter session code"
                  value={sessionCode}
                  onChange={(e) => setSessionCode(e.target.value.toUpperCase())}
                  className="text-center text-lg font-mono tracking-wider bg-background border-border"
                  maxLength={8}
                />
                <p className="text-xs text-muted-foreground text-center">
                  Get the session code from your speaker
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-foreground">Live Text-to-Speech</label>
                    <p className="text-xs text-muted-foreground">Automatically play translated audio</p>
                  </div>
                  <Switch
                    checked={liveTextToSpeech}
                    onCheckedChange={setLiveTextToSpeech}
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-foreground">Volume</label>
                    <span className="text-sm text-muted-foreground">{volume[0]}%</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Volume2 className="w-4 h-4 text-muted-foreground" />
                    <Slider
                      value={volume}
                      onValueChange={setVolume}
                      max={100}
                      step={5}
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>

              <Button
                onClick={joinSession}
                disabled={connectionStatus === "connecting"}
                className="w-full bg-gradient-primary hover:opacity-90 text-white font-medium disabled:opacity-50"
                size="lg"
              >
                {connectionStatus === "connecting" ? "Connecting..." : "Join Session"}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gradient-surface p-4">
        <div className="container mx-auto max-w-4xl pt-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Translation Session</h1>
              <p className="text-muted-foreground">Connected to: <span className="font-mono font-medium text-primary">{sessionCode}</span></p>
              <div className="flex items-center gap-4 mt-2">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    connectionStatus === "connected" ? "bg-green-500" :
                    connectionStatus === "connecting" ? "bg-yellow-500 animate-pulse" :
                    "bg-red-500"
                  }`}></div>
                  <span className="text-sm text-muted-foreground">
                    {connectionStatus === "connected" ? "Connected" :
                     connectionStatus === "connecting" ? "Connecting..." :
                     "Disconnected"}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">{audienceCount} audience</span>
                </div>
                {sessionInfo && (
                  <div className="text-sm text-muted-foreground">
                    {sessionInfo.input_language} → {sessionInfo.output_language}
                  </div>
                )}
              </div>
            </div>
            <Button
              variant="outline"
              onClick={disconnectSession}
              className="text-destructive border-destructive/30 hover:bg-destructive/10"
            >
              Disconnect
            </Button>
          </div>

        <div className="grid gap-6">
          <div className="flex items-center justify-between p-4 bg-accent/10 rounded-lg border border-accent/20">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-foreground">Live Translation Active</span>
              {isPlayingAudio && (
                <div className="flex items-center gap-2 ml-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-blue-600">Playing Audio</span>
                </div>
              )}
              {audioQueue.length > 0 && (
                <div className="flex items-center gap-2 ml-2">
                  <span className="text-xs text-orange-600">Queue: {audioQueue.length}</span>
                </div>
              )}
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Switch
                  checked={liveTextToSpeech}
                  onCheckedChange={setLiveTextToSpeech}
                />
                <span className="text-sm text-muted-foreground">Auto-play</span>
              </div>
              <div className="flex items-center gap-2 min-w-24">
                <Volume2 className="w-4 h-4 text-muted-foreground" />
                <Slider
                  value={volume}
                  onValueChange={(newVolume) => {
                    setVolume(newVolume);
                    // Update current audio volume if playing
                    if (currentAudio) {
                      currentAudio.volume = newVolume[0] / 100;
                    }
                  }}
                  max={100}
                  step={5}
                  className="w-16"
                />
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <Card className="shadow-medium border-border/50">
              <CardHeader>
                <CardTitle className="text-lg">Original</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="min-h-32 p-4 bg-muted/30 rounded-lg border border-border/50">
                  {originalText ? (
                    <p className="text-foreground leading-relaxed">{originalText}</p>
                  ) : (
                    <p className="text-muted-foreground italic">Waiting for speaker...</p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-medium border-border/50">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">Translation</CardTitle>
                {translatedText && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => speakText(translatedText)}
                    className="text-primary border-primary/30 hover:bg-primary/10"
                  >
                    <Volume2 className="w-4 h-4" />
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                <div className="min-h-32 p-4 bg-primary/5 rounded-lg border border-primary/20">
                  {translatedText ? (
                    <p className="text-foreground leading-relaxed">{translatedText}</p>
                  ) : (
                    <p className="text-muted-foreground italic">Translation will appear here...</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>

      <ConfirmationModal
        isOpen={showSessionEndedModal}
        onClose={() => setShowSessionEndedModal(false)}
        onConfirm={handleSessionEnded}
        title="Session Ended"
        description="The session has been ended by the speaker. You will be redirected to the homepage."
        confirmText="OK"
        cancelText=""
        variant="default"
      />
    </>
  );
};

export default Audience;